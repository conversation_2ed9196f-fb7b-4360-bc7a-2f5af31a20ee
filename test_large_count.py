#!/usr/bin/env python3

import json
import tempfile
import os
import duckdb
import time

def create_large_json_file(filename, num_records=1000):
    """Create a large JSON file with many records."""
    print(f"Creating JSON file with {num_records} records...")
    
    # Write records one by one to avoid loading everything into memory
    with open(filename, 'w') as f:
        f.write('[')
        for i in range(num_records):
            if i > 0:
                f.write(',')
            record = {
                "id": i,
                "name": f"Person_{i}",
                "large_unused_field": "x" * 2000,  # 2KB of unused data per record
                "another_large_field": "y" * 2000,  # Another 2KB of unused data
                "metadata": {
                    "created": f"2024-01-{(i % 30) + 1:02d}",
                    "tags": [f"tag_{j}" for j in range(20)],  # More tags
                    "description": "z" * 1000  # 1KB more
                }
            }
            json.dump(record, f)
        f.write(']')
    
    return os.path.getsize(filename)

def test_large_count_performance():
    """Test performance and memory usage for COUNT queries on large files."""
    
    # Create test file
    fd, temp_file = tempfile.mkstemp(suffix='.json')
    os.close(fd)
    
    try:
        file_size = create_large_json_file(temp_file, 500)  # 500 records, ~2.5MB file
        print(f"Created test file: {file_size / 1024 / 1024:.1f} MB")
        
        # Test with DuckDB extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        print("\n=== Test 1: COUNT(*) query on large file ===")
        start_time = time.time()
        result = conn.execute(f'SELECT COUNT(*) FROM streaming_json_reader("{temp_file}")').fetchall()
        end_time = time.time()
        print(f"Result: {result[0][0]} records")
        print(f"Time: {end_time - start_time:.2f} seconds")
        
        print("\n=== Test 2: SELECT id (minimal data) ===")
        start_time = time.time()
        result = conn.execute(f'SELECT COUNT(id) FROM streaming_json_reader("{temp_file}")').fetchall()
        end_time = time.time()
        print(f"Result: {result[0][0]} records")
        print(f"Time: {end_time - start_time:.2f} seconds")
        
        print("\n=== Test 3: Compare with DuckDB default (if possible) ===")
        try:
            start_time = time.time()
            result = conn.execute(f'SELECT COUNT(*) FROM read_json_auto("{temp_file}")').fetchall()
            end_time = time.time()
            print(f"DuckDB default result: {result[0][0]} records")
            print(f"DuckDB default time: {end_time - start_time:.2f} seconds")
        except Exception as e:
            print(f"DuckDB default failed: {e}")
        
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)

if __name__ == "__main__":
    test_large_count_performance()
