#!/usr/bin/env python3

import json
import tempfile
import os
import duckdb

def test_simple_array():
    """Test the simplest case that's failing: [[1, 2, 3], [4, 5, 6]]"""
    
    # Create test data
    data = [[1, 2, 3], [4, 5, 6]]
    
    # Write to temp file
    fd, temp_file = tempfile.mkstemp(suffix='.json')
    os.close(fd)
    
    try:
        with open(temp_file, 'w') as f:
            json.dump(data, f)
        
        print(f"Test data: {data}")
        print(f"File: {temp_file}")
        
        # Test with DuckDB default
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        
        print("\n=== DuckDB Default ===")
        try:
            result = conn.execute(f"SELECT * FROM read_json_auto('{temp_file}')").fetchall()
            print(f"Success: {result}")
        except Exception as e:
            print(f"Error: {e}")
        
        print("\n=== Our Extension ===")
        try:
            conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
            result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
            print(f"Success: {result}")
        except Exception as e:
            print(f"Error: {e}")
            
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)

if __name__ == "__main__":
    test_simple_array()
