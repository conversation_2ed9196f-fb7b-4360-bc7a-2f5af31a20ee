#!/usr/bin/env python3

import json
import tempfile
import os
import duckdb

def test_projection_pushdown():
    """Test that projection pushdown is working correctly."""
    
    # Create test data with multiple fields
    data = [
        {"id": 1, "name": "<PERSON>", "age": 30, "city": "New York"},
        {"id": 2, "name": "<PERSON>", "age": 25, "city": "San Francisco"},
        {"id": 3, "name": "<PERSON>", "age": 35, "city": "Chicago"}
    ]
    
    # Write to temp file
    fd, temp_file = tempfile.mkstemp(suffix='.json')
    os.close(fd)
    
    try:
        with open(temp_file, 'w') as f:
            json.dump(data, f)
        
        print(f"Test data: {data}")
        print(f"File: {temp_file}")
        
        # Test with DuckDB extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        print("\n=== Test 1: SELECT * (all columns) ===")
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Result: {result}")
        
        print("\n=== Test 2: SELECT id (single column projection) ===")
        result = conn.execute(f'SELECT id FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Result: {result}")
        
        print("\n=== Test 3: SELECT name, city (multi-column projection) ===")
        result = conn.execute(f'SELECT name, city FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Result: {result}")
        
        print("\n=== Test 4: COUNT(*) query ===")
        result = conn.execute(f'SELECT COUNT(*) FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Result: {result}")
        
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)

if __name__ == "__main__":
    test_projection_pushdown()
