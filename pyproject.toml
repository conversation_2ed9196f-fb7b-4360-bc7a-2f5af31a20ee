[project]
name = "extension-template-rs"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "memray>=1.17.2",
    "psutil>=7.0.0",
    "pytest-memray>=1.7.0",
]

[dependency-groups]
dev = [
    "duckdb>=1.3.1",
    "pytest>=8.4.1",
]

# pytest configuration
[tool.pytest.ini_options]
addopts = "-v --tb=short"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# Memray configuration
markers = [
    "limit_memory: Set memory limit for test using pytest-memray",
    "slow: Mark test as slow running",
]

# Filter warnings
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
