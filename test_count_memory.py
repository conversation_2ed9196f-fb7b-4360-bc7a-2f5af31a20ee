#!/usr/bin/env python3

import json
import tempfile
import os
import duckdb

def create_large_json_file(filename, num_records=100):
    """Create a large JSON file with many records."""
    data = []
    for i in range(num_records):
        record = {
            "id": i,
            "name": f"Person_{i}",
            "large_unused_field": "x" * 1000,  # 1KB of unused data per record
            "another_large_field": "y" * 1000,  # Another 1KB of unused data
            "metadata": {
                "created": f"2024-01-{(i % 30) + 1:02d}",
                "tags": [f"tag_{j}" for j in range(10)],
                "description": "z" * 500  # 500 bytes more
            }
        }
        data.append(record)
    
    with open(filename, 'w') as f:
        json.dump(data, f)
    
    return os.path.getsize(filename)

def test_count_memory_usage():
    """Test memory usage for COUNT queries with projection pushdown."""
    
    # Create test file
    fd, temp_file = tempfile.mkstemp(suffix='.json')
    os.close(fd)
    
    try:
        file_size = create_large_json_file(temp_file, 100)  # Start with smaller file
        print(f"Created test file: {file_size / 1024:.1f} KB")
        
        # Test with DuckDB extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        print("\n=== Test 1: COUNT(*) query ===")
        result = conn.execute(f'SELECT COUNT(*) FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Result: {result[0][0]} records")
        
        print("\n=== Test 2: SELECT id (minimal projection) ===")
        result = conn.execute(f'SELECT id FROM streaming_json_reader("{temp_file}") LIMIT 5').fetchall()
        print(f"Result: {result}")
        
        print("\n=== Test 3: SELECT large_unused_field (should use more memory) ===")
        result = conn.execute(f'SELECT large_unused_field FROM streaming_json_reader("{temp_file}") LIMIT 3').fetchall()
        print(f"Result length: {len(result)} records, first field length: {len(result[0][0])} chars")
        
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)

if __name__ == "__main__":
    test_count_memory_usage()
