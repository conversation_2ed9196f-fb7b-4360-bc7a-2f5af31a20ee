#!/usr/bin/env python3
"""
Test suite for streaming JSON reader DuckDB extension.
Defines target functionality and expected behavior.
"""

import pytest
import duckdb
import json
import os
import tempfile
from typing import Dict, List, Any


class TestStreamingJsonReader:
    """Test suite for streaming JSON reader extension."""
    
    @pytest.fixture(scope="class")
    def duckdb_conn(self):
        """Setup DuckDB connection with extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        extension_path = "build/debug/streaming_json_reader.duckdb_extension"
        if not os.path.exists(extension_path):
            pytest.skip(f"Extension not found at {extension_path}")
        conn.execute(f'LOAD "{extension_path}"')
        return conn
    
    @pytest.fixture
    def temp_json_file(self):
        """Create temporary JSON file for testing."""
        fd, path = tempfile.mkstemp(suffix='.json')
        yield path
        os.close(fd)
        os.unlink(path)
    
    def create_json_file(self, data: Any, filepath: str):
        """Helper to create JSON test files."""
        with open(filepath, 'w') as f:
            json.dump(data, f)
    
    # Basic JSON Object Tests (0 levels of nesting)
    
    def test_simple_object_basic_types(self, duckdb_conn, temp_json_file):
        """Test simple JSON object with all basic types."""
        data = {
            "string_field": "test_value",
            "number_field": 42.5,
            "integer_field": 100,
            "boolean_field": True,
            "null_field": None
        }
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Verify structure
        assert len(result) == 1
        assert len(result[0]) == 5
        
        # Verify column types
        column_types = {col[0]: col[1] for col in columns}
        assert column_types["string_field"] == "VARCHAR"
        assert column_types["number_field"] == "DOUBLE"
        assert column_types["integer_field"] == "DOUBLE"
        assert column_types["boolean_field"] == "BOOLEAN"
        assert column_types["null_field"] == "VARCHAR"
        
        # Verify values (columns are in order of first appearance in schema)
        row = result[0]
        assert row[0] == "test_value"  # string_field
        assert abs(row[1] - 42.5) < 0.001  # number_field
        assert abs(row[2] - 100.0) < 0.001  # integer_field
        assert row[3] is True  # boolean_field
        assert row[4] is None  # null_field

    def test_array_objects_mixed_field_order(self, duckdb_conn, temp_json_file):
        """Test array of objects where each object has fields in different order.

        JSON objects are unordered by specification, so we must handle cases where
        the same logical object structure appears with fields in different orders.
        Schema should be determined by first observation, but all objects should
        be parsed correctly regardless of field order.
        """
        data = [
            {"a": 1, "b": "x"},      # First object: a, b order
            {"b": "y", "a": 10}      # Second object: b, a order (different order)
        ]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        # Verify structure
        assert len(result) == 2
        assert len(result[0]) == 2

        # Verify column types
        column_types = {col[0]: col[1] for col in columns}
        assert column_types["a"] == "DOUBLE"
        assert column_types["b"] == "VARCHAR"

        # Verify values - schema order should be based on first object (a, b)
        row1 = result[0]
        assert abs(row1[0] - 1.0) < 0.001  # a field
        assert row1[1] == "x"  # b field

        row2 = result[1]
        assert abs(row2[0] - 10.0) < 0.001  # a field
        assert row2[1] == "y"  # b field

    def test_empty_object(self, duckdb_conn, temp_json_file):
        """Test empty JSON object."""
        data = {}
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        assert len(result) == 1
        assert len(columns) == 1  # Single "json" column for empty object
        assert columns[0][0] == "json"  # Column name should be "json"
        assert result[0][0] == "{}"  # Should contain empty object as string
    
    # One Level Nesting Tests
    
    def test_struct_field_access(self, duckdb_conn, temp_json_file):
        """Test STRUCT field access with dot notation."""
        data = {
            "metadata": {
                "name": "test_dataset",
                "version": "1.0",
                "count": 42
            },
            "simple_field": "value"
        }
        self.create_json_file(data, temp_json_file)
        
        # Test full object access
        result = duckdb_conn.execute(f'SELECT metadata FROM streaming_json_reader("{temp_json_file}")').fetchall()
        metadata = result[0][0]
        assert isinstance(metadata, dict)
        assert metadata["name"] == "test_dataset"
        assert metadata["version"] == "1.0"
        assert metadata["count"] == 42
        
        # Test STRUCT field access
        result = duckdb_conn.execute(f'SELECT metadata.name FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "test_dataset"
        
        result = duckdb_conn.execute(f'SELECT metadata.count FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert abs(result[0][0] - 42.0) < 0.001
    
    def test_array_basic_elements(self, duckdb_conn, temp_json_file):
        """Test array with basic element types."""
        data = {
            "numbers": [1, 2, 3, 4.5],
            "strings": ["a", "b", "c"],
            "booleans": [True, False, True]
        }
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Verify column types
        column_types = {col[0]: col[1] for col in columns}
        assert column_types["numbers"] == "DOUBLE[]"
        assert column_types["strings"] == "VARCHAR[]"
        assert column_types["booleans"] == "BOOLEAN[]"
        
        # Verify values
        row = result[0]
        assert row[0] == [1.0, 2.0, 3.0, 4.5]  # numbers
        assert row[1] == ["a", "b", "c"]  # strings
        assert row[2] == [True, False, True]  # booleans
    
    # Two Level Nesting Tests
    
    def test_nested_struct_access(self, duckdb_conn, temp_json_file):
        """Test nested STRUCT access."""
        data = {
            "config": {
                "database": {
                    "host": "localhost",
                    "port": 5432
                },
                "cache": {
                    "enabled": True,
                    "size": 1024
                }
            }
        }
        self.create_json_file(data, temp_json_file)
        
        # Test nested field access
        result = duckdb_conn.execute(f'SELECT config.database.host FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "localhost"
        
        result = duckdb_conn.execute(f'SELECT config.database.port FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert abs(result[0][0] - 5432.0) < 0.001
        
        result = duckdb_conn.execute(f'SELECT config.cache.enabled FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] is True
    
    def test_array_of_structs(self, duckdb_conn, temp_json_file):
        """Test array containing STRUCT elements."""
        data = {
            "users": [
                {"id": 1, "name": "Alice", "active": True},
                {"id": 2, "name": "Bob", "active": False},
                {"id": 3, "name": "Charlie", "active": True}
            ]
        }
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT users FROM streaming_json_reader("{temp_json_file}")').fetchall()
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Verify column type
        column_types = {col[0]: col[1] for col in columns}
        assert "STRUCT" in column_types["users"] and "[]" in column_types["users"]
        
        # Verify array access
        users = result[0][0]
        assert len(users) == 3
        assert users[0]["name"] == "Alice"
        assert users[1]["id"] == 2
        assert users[2]["active"] is True
        
        # Test array element access
        result = duckdb_conn.execute(f'SELECT users[1].name FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "Alice"  # DuckDB uses 1-based indexing
    
    # Three Level Nesting Tests
    
    def test_deep_nested_structures(self, duckdb_conn, temp_json_file):
        """Test three levels of nesting."""
        data = {
            "company": {
                "departments": {
                    "engineering": {
                        "head": "John Doe",
                        "budget": 1000000,
                        "projects": ["project_a", "project_b"]
                    }
                }
            }
        }
        self.create_json_file(data, temp_json_file)
        
        # Test deep field access
        result = duckdb_conn.execute(f'SELECT company.departments.engineering.head FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "John Doe"
        
        result = duckdb_conn.execute(f'SELECT company.departments.engineering.budget FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert abs(result[0][0] - 1000000.0) < 0.001

    # Projection Pushdown Tests

    def test_projection_pushdown_single_field(self, duckdb_conn, temp_json_file):
        """Test projection pushdown with single field selection."""
        data = {
            "field_a": "value_a",
            "field_b": "value_b",
            "field_c": "value_c"
        }
        self.create_json_file(data, temp_json_file)

        # Should only process field_a
        result = duckdb_conn.execute(f'SELECT field_a FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "value_a"

        # Verify only one column in result
        columns = duckdb_conn.execute(f'DESCRIBE SELECT field_a FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert len(columns) == 1
        assert columns[0][0] == "field_a"

    def test_projection_pushdown_struct_field(self, duckdb_conn, temp_json_file):
        """Test projection pushdown with STRUCT field selection."""
        data = {
            "metadata": {"name": "test", "version": "1.0"},
            "data": {"values": [1, 2, 3]},
            "unused": {"large": "data" * 1000}
        }
        self.create_json_file(data, temp_json_file)

        # Should only process metadata field
        result = duckdb_conn.execute(f'SELECT metadata FROM streaming_json_reader("{temp_json_file}")').fetchall()
        metadata = result[0][0]
        assert metadata["name"] == "test"
        assert metadata["version"] == "1.0"

    # Array Processing Tests

    def test_array_flattening(self, duckdb_conn, temp_json_file):
        """Test array flattening into rows."""
        # TODO it seems like this test does not follow the design decisions
        # Assess whether the query should actually be something containing an unnest
        data = [
            {"id": 1, "name": "Alice"},
            {"id": 2, "name": "Bob"},
            {"id": 3, "name": "Charlie"}
        ]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        # Should create 3 rows
        assert len(result) == 3
        assert result[0][0] == 1  # id
        assert result[0][1] == "Alice"  # name
        assert result[1][0] == 2
        assert result[1][1] == "Bob"
        assert result[2][0] == 3
        assert result[2][1] == "Charlie"

    @pytest.mark.skip(reason="Causes high disk usage or high memory usage and is extremely slow")
    def test_nested_array_access(self, duckdb_conn, temp_json_file):
        """Test access to nested arrays."""
        data = {
            "matrix": [[1, 2], [3, 4], [5, 6]]
        }
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT matrix FROM streaming_json_reader("{temp_json_file}")').fetchall()
        matrix = result[0][0]
        assert matrix == [[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]]

        # Test element access
        result = duckdb_conn.execute(f'SELECT matrix[1][2] FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert abs(result[0][0] - 2.0) < 0.001

    # Memory Efficiency Tests

    def test_large_json_memory_efficiency(self, duckdb_conn, temp_json_file):
        """Test memory efficiency with large JSON files."""
        # Create large JSON with many fields
        data = {f"field_{i}": f"value_{i}" for i in range(1000)}
        data["target_field"] = "target_value"
        self.create_json_file(data, temp_json_file)

        # Should only process target_field, not all 1000 fields
        result = duckdb_conn.execute(f'SELECT target_field FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == "target_value"

    def test_streaming_large_array(self, duckdb_conn, temp_json_file):
        """Test streaming processing of large arrays."""
        # Create array with many elements
        data = [{"id": i, "value": f"item_{i}"} for i in range(100)]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT COUNT(*) FROM streaming_json_reader("{temp_json_file}")').fetchall()
        assert result[0][0] == 100

        # Test specific element access
        result = duckdb_conn.execute(f'SELECT id, value FROM streaming_json_reader("{temp_json_file}") WHERE id = 50').fetchall()
        assert len(result) == 1
        assert result[0][0] == 50
        assert result[0][1] == "item_50"

    # Error Handling Tests

    def test_malformed_json_error(self, duckdb_conn, temp_json_file):
        """Test error handling for malformed JSON."""
        with open(temp_json_file, 'w') as f:
            f.write('{"invalid": json,}')

        with pytest.raises(Exception) as exc_info:
            duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        assert "malformed" in str(exc_info.value).lower() or "json" in str(exc_info.value).lower()

    def test_nonexistent_file_error(self, duckdb_conn):
        """Test error handling for non-existent files."""
        with pytest.raises(Exception) as exc_info:
            duckdb_conn.execute('SELECT * FROM streaming_json_reader("nonexistent.json")').fetchall()

        error_message = str(exc_info.value).lower()
        assert any(keyword in error_message for keyword in [
            "not found", "no such file", "does not exist", "file i/o error"
        ])

    def test_empty_file_handling(self, duckdb_conn, temp_json_file):
        """Test handling of empty files."""
        with open(temp_json_file, 'w') as f:
            f.write('')

        with pytest.raises(Exception) as exc_info:
            duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        assert "empty" in str(exc_info.value).lower() or "malformed" in str(exc_info.value).lower()

    # Data Type Precision Tests

    def test_numeric_precision(self, duckdb_conn, temp_json_file):
        """Test numeric precision handling."""
        data = {
            "small_int": 1,
            "large_int": 9223372036854775807,
            "small_float": 0.1,
            "large_float": 1.7976931348623157e+308,
            "scientific": 1.23e-10
        }
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        row = result[0]

        assert abs(row[0] - 1.0) < 0.001
        assert abs(row[1] - 9223372036854775807.0) < 1.0
        assert abs(row[2] - 0.1) < 0.001
        assert abs(row[4] - 1.23e-10) < 1e-15

    def test_boolean_values(self, duckdb_conn, temp_json_file):
        """Test boolean value handling."""
        data = {
            "true_val": True,
            "false_val": False
        }
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        row = result[0]

        assert row[0] is True
        assert row[1] is False

        # Test boolean column type
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        column_types = {col[0]: col[1] for col in columns}
        assert column_types["true_val"] == "BOOLEAN"
        assert column_types["false_val"] == "BOOLEAN"

    # Multi-dimensional Array Tests


    def test_2d_array_numbers(self, duckdb_conn, temp_json_file):
        """Test 2D array of numbers - this should expose the data duplication bug."""
        # Test data: [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
        # Expected: Two rows, each containing a 2D array
        data = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("2D Array result:", result)

        # Should have 2 rows (root array flattening)
        assert len(result) == 2

        # First row should contain [[1, 2], [3, 4]]
        first_row = result[0][0]
        assert len(first_row) == 2
        assert first_row[0] == [1, 2], f"Expected [1, 2], got {first_row[0]}"
        assert first_row[1] == [3, 4], f"Expected [3, 4], got {first_row[1]}"

        # Second row should contain [[5, 6], [7, 8]]
        second_row = result[1][0]
        assert len(second_row) == 2
        assert second_row[0] == [5, 6], f"Expected [5, 6], got {second_row[0]}"
        assert second_row[1] == [7, 8], f"Expected [7, 8], got {second_row[1]}"

    def test_3d_array_numbers(self, duckdb_conn, temp_json_file):
        """Test 3D array of numbers - single root array containing 3D structure."""
        # Single 3D array: [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
        data = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("3D Array result:", result)

        # Should have 2 rows (root array flattening: each 2D array becomes a row)
        assert len(result) == 2

        # First row should contain [[1, 2], [3, 4]]
        first_row = result[0][0]
        assert len(first_row) == 2
        assert first_row[0] == [1, 2], f"Expected [1, 2], got {first_row[0]}"
        assert first_row[1] == [3, 4], f"Expected [3, 4], got {first_row[1]}"

        # Second row should contain [[5, 6], [7, 8]]
        second_row = result[1][0]
        assert len(second_row) == 2
        assert second_row[0] == [5, 6], f"Expected [5, 6], got {second_row[0]}"
        assert second_row[1] == [7, 8], f"Expected [7, 8], got {second_row[1]}"

    def test_irregular_2d_arrays(self, duckdb_conn, temp_json_file):
        """Test 2D arrays with different sub-array lengths."""
        # Arrays with varying lengths: [[1, 2, 3], [4]], [[5, 6], [7, 8, 9, 10]]
        data = [[[1, 2, 3], [4]], [[5, 6], [7, 8, 9, 10]]]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Irregular 2D Array result:", result)

        # Should have 2 rows
        assert len(result) == 2

        # First row: [[1, 2, 3], [4]]
        first_row = result[0][0]
        assert len(first_row) == 2
        assert first_row[0] == [1, 2, 3], f"Expected [1, 2, 3], got {first_row[0]}"
        assert first_row[1] == [4], f"Expected [4], got {first_row[1]}"

        # Second row: [[5, 6], [7, 8, 9, 10]]
        second_row = result[1][0]
        assert len(second_row) == 2
        assert second_row[0] == [5, 6], f"Expected [5, 6], got {second_row[0]}"
        assert second_row[1] == [7, 8, 9, 10], f"Expected [7, 8, 9, 10], got {second_row[1]}"

    def test_varying_row_lengths(self, duckdb_conn, temp_json_file):
        """Test rows with different numbers of sub-arrays."""
        # First row has 3 sub-arrays, second row has 1 sub-array
        data = [[[1, 2], [3, 4], [5, 6]], [[7, 8]]]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Varying row lengths result:", result)

        # Should have 2 rows
        assert len(result) == 2

        # First row: [[1, 2], [3, 4], [5, 6]] - 3 sub-arrays
        first_row = result[0][0]
        assert len(first_row) == 3
        assert first_row[0] == [1, 2], f"Expected [1, 2], got {first_row[0]}"
        assert first_row[1] == [3, 4], f"Expected [3, 4], got {first_row[1]}"
        assert first_row[2] == [5, 6], f"Expected [5, 6], got {first_row[2]}"

        # Second row: [[7, 8]] - 1 sub-array
        second_row = result[1][0]
        assert len(second_row) == 1
        assert second_row[0] == [7, 8], f"Expected [7, 8], got {second_row[0]}"

    def test_4d_array_numbers(self, duckdb_conn, temp_json_file):
        """Test 4D array of numbers."""
        # 4D structure: [[[[1, 2]], [[3, 4]]], [[[5, 6]], [[7, 8]]]]
        data = [[[[[1, 2]], [[3, 4]]], [[[5, 6]], [[7, 8]]]]]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("4D Array result:", result)

        # Should have 1 row (single root element)
        assert len(result) == 1

        # The row should contain the full 4D structure
        array_4d = result[0][0]
        assert len(array_4d) == 2  # Two 3D arrays

        # First 3D array: [[[1, 2]], [[3, 4]]]
        first_3d = array_4d[0]
        assert len(first_3d) == 2
        assert first_3d[0] == [[1, 2]], f"Expected [[1, 2]], got {first_3d[0]}"
        assert first_3d[1] == [[3, 4]], f"Expected [[3, 4]], got {first_3d[1]}"

        # Second 3D array: [[[5, 6]], [[7, 8]]]
        second_3d = array_4d[1]
        assert len(second_3d) == 2
        assert second_3d[0] == [[5, 6]], f"Expected [[5, 6]], got {second_3d[0]}"
        assert second_3d[1] == [[7, 8]], f"Expected [[7, 8]], got {second_3d[1]}"

    def test_mixed_depth_arrays(self, duckdb_conn, temp_json_file):
        """Test arrays with mixed nesting depths - this should fail due to inconsistent schema."""
        # Mix of different types: [1, [2, 3], [[4, 5]]]
        # This represents an inconsistent schema:
        # - 1 is a number
        # - [2, 3] is a 1D array
        # - [[4, 5]] is a 2D array
        # Our extension should reject this as it cannot infer a consistent type
        data = [[1, [2, 3], [[4, 5]]]]
        self.create_json_file(data, temp_json_file)

        # This should fail with a schema error due to inconsistent types
        with pytest.raises(duckdb.InvalidInputException) as exc_info:
            duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()

        # Verify the error message indicates a type conflict or schema issue
        error_message = str(exc_info.value).lower()
        assert any(keyword in error_message for keyword in [
            'type', 'schema', 'conflict', 'merge', 'inconsistent', 'parsing'
        ]), f"Expected schema/type error, got: {exc_info.value}"

    @pytest.mark.skip(reason="Causes high disk usage or high memory usage and is extremely slow")
    def test_empty_subarrays(self, duckdb_conn, temp_json_file):
        """Test arrays containing empty sub-arrays."""
        # Arrays with empty sub-arrays: [[], [1, 2]], [[3, 4], []]
        data = [[[], [1, 2]], [[3, 4], []]]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Empty subarrays result:", result)

        # Should have 2 rows
        assert len(result) == 2

        # First row: [[], [1, 2]]
        first_row = result[0][0]
        assert len(first_row) == 2
        assert first_row[0] == [], f"Expected [], got {first_row[0]}"
        assert first_row[1] == [1, 2], f"Expected [1, 2], got {first_row[1]}"

        # Second row: [[3, 4], []]
        second_row = result[1][0]
        assert len(second_row) == 2
        assert second_row[0] == [3, 4], f"Expected [3, 4], got {second_row[0]}"
        assert second_row[1] == [], f"Expected [], got {second_row[1]}"

    def test_single_element_arrays(self, duckdb_conn, temp_json_file):
        """Test arrays with single elements at various levels."""
        # Single element arrays: [[[1]], [[2]]]
        data = [[[[1]], [[2]]]]
        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Single element arrays result:", result)

        # Should have 1 row
        assert len(result) == 1

        # The row should contain the nested single elements
        nested_array = result[0][0]
        assert len(nested_array) == 2
        assert nested_array[0] == [[1]], f"Expected [[1]], got {nested_array[0]}"
        assert nested_array[1] == [[2]], f"Expected [[2]], got {nested_array[1]}"

    @pytest.mark.skip(reason="Causes high disk usage or high memory usage and is extremely slow")
    def test_large_multidimensional_array(self, duckdb_conn, temp_json_file):
        """Test larger multi-dimensional arrays to verify scalability."""
        # Create a larger 3D array: 3 rows, each with 4 sub-arrays, each with 3 elements
        data = []
        for i in range(3):
            row = []
            for j in range(4):
                sub_array = [i*12 + j*3 + k for k in range(3)]
                row.append(sub_array)
            data.append(row)

        self.create_json_file(data, temp_json_file)

        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Large multidimensional array result length:", len(result))

        # Should have 3 rows
        assert len(result) == 3

        # Verify structure of first row
        first_row = result[0][0]
        assert len(first_row) == 4  # 4 sub-arrays
        assert first_row[0] == [0, 1, 2], f"Expected [0, 1, 2], got {first_row[0]}"
        assert first_row[1] == [3, 4, 5], f"Expected [3, 4, 5], got {first_row[1]}"
        assert first_row[2] == [6, 7, 8], f"Expected [6, 7, 8], got {first_row[2]}"
        assert first_row[3] == [9, 10, 11], f"Expected [9, 10, 11], got {first_row[3]}"

        # Verify structure of last row
        last_row = result[2][0]
        assert len(last_row) == 4  # 4 sub-arrays
        assert last_row[0] == [24, 25, 26], f"Expected [24, 25, 26], got {last_row[0]}"
        assert last_row[3] == [33, 34, 35], f"Expected [33, 34, 35], got {last_row[3]}"
