#!/usr/bin/env python3

import pytest
import json
import tempfile
import os
import duckdb
import time
from typing import List, Dict, Any


class TestStreamingMemoryEfficiency:
    """Test memory efficiency of the streaming JSON reader with various query patterns."""

    @pytest.fixture
    def duckdb_conn(self):
        """Create a DuckDB connection with the streaming JSON extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        return conn

    def create_large_json_with_unused_data(self, filename: str, num_records: int = 1000) -> int:
        """Create a large JSON file with significant unused data per record."""
        print(f"Creating JSON file with {num_records} records...")
        
        # Write records one by one to avoid loading everything into memory during test setup
        with open(filename, 'w') as f:
            f.write('[')
            for i in range(num_records):
                if i > 0:
                    f.write(',')
                
                record = {
                    "id": i,
                    "small_field": f"item_{i}",
                    "large_unused_field_1": "x" * 2000,  # 2KB of unused data
                    "large_unused_field_2": "y" * 2000,  # Another 2KB of unused data
                    "large_unused_field_3": "z" * 2000,  # Another 2KB of unused data
                    "metadata": {
                        "created": f"2024-01-{(i % 30) + 1:02d}",
                        "tags": [f"tag_{j}" for j in range(50)],  # Large array
                        "description": "w" * 1000,  # 1KB more
                        "extra_data": {
                            "field1": "a" * 500,
                            "field2": "b" * 500,
                            "field3": "c" * 500,
                        }
                    }
                }
                json.dump(record, f)
            f.write(']')
        
        return os.path.getsize(filename)

    @pytest.mark.limit_memory("50 MB")
    def test_count_query_memory_efficiency(self, duckdb_conn):
        """Test that COUNT queries use minimal memory regardless of data size."""
        
        fd, test_file = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        
        try:
            file_size = self.create_large_json_with_unused_data(test_file, 500)
            
            print(f"\nTest file size: {file_size / 1024 / 1024:.2f} MB")
            print(f"Memory limit: 50MB")
            print(f"Records: 500 (each ~10KB with unused data)")
            
            # COUNT query should be extremely memory efficient
            start_time = time.time()
            result = duckdb_conn.execute(f'SELECT COUNT(*) FROM streaming_json_reader("{test_file}")').fetchall()
            end_time = time.time()
            
            assert result[0][0] == 500
            
            print(f"SUCCESS: COUNT query processed {result[0][0]} records within memory limit")
            print(f"Time: {end_time - start_time:.2f} seconds")
            
        finally:
            if os.path.exists(test_file):
                os.unlink(test_file)

    @pytest.mark.limit_memory("50 MB")
    def test_sum_query_memory_efficiency(self, duckdb_conn):
        """Test that SUM queries on small fields use minimal memory."""
        
        fd, test_file = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        
        try:
            file_size = self.create_large_json_with_unused_data(test_file, 500)
            
            print(f"\nTest file size: {file_size / 1024 / 1024:.2f} MB")
            print(f"Memory limit: 50MB")
            
            # SUM query on small field should be memory efficient
            start_time = time.time()
            result = duckdb_conn.execute(f'SELECT SUM(id) FROM streaming_json_reader("{test_file}")').fetchall()
            end_time = time.time()
            
            expected_sum = sum(range(500))  # 0 + 1 + 2 + ... + 499
            assert result[0][0] == expected_sum
            
            print(f"SUCCESS: SUM query processed within memory limit")
            print(f"Result: {result[0][0]} (expected: {expected_sum})")
            print(f"Time: {end_time - start_time:.2f} seconds")
            
        finally:
            if os.path.exists(test_file):
                os.unlink(test_file)

    @pytest.mark.limit_memory("50 MB")
    def test_minimal_projection_memory_efficiency(self, duckdb_conn):
        """Test that queries selecting only small fields use minimal memory."""
        
        fd, test_file = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        
        try:
            file_size = self.create_large_json_with_unused_data(test_file, 500)
            
            print(f"\nTest file size: {file_size / 1024 / 1024:.2f} MB")
            print(f"Memory limit: 50MB")
            
            # Query selecting only small fields
            start_time = time.time()
            result = duckdb_conn.execute(f'SELECT id, small_field FROM streaming_json_reader("{test_file}") LIMIT 10').fetchall()
            end_time = time.time()
            
            assert len(result) == 10
            assert result[0][0] == 0
            assert result[0][1] == "item_0"
            assert result[9][0] == 9
            assert result[9][1] == "item_9"
            
            print(f"SUCCESS: Minimal projection query processed within memory limit")
            print(f"Sample results: {result[:3]}")
            print(f"Time: {end_time - start_time:.2f} seconds")
            
        finally:
            if os.path.exists(test_file):
                os.unlink(test_file)

    @pytest.mark.limit_memory("100 MB")
    def test_large_field_projection_uses_more_memory(self, duckdb_conn):
        """Test that queries selecting large fields use more memory (but still reasonable)."""
        
        fd, test_file = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        
        try:
            file_size = self.create_large_json_with_unused_data(test_file, 200)  # Smaller dataset for large field test
            
            print(f"\nTest file size: {file_size / 1024 / 1024:.2f} MB")
            print(f"Memory limit: 100MB")
            
            # Query selecting large field - should use more memory but still be reasonable
            start_time = time.time()
            result = duckdb_conn.execute(f'SELECT large_unused_field_1 FROM streaming_json_reader("{test_file}") LIMIT 5').fetchall()
            end_time = time.time()
            
            assert len(result) == 5
            assert len(result[0][0]) == 2000  # 2KB field
            
            print(f"SUCCESS: Large field projection processed within memory limit")
            print(f"Field length: {len(result[0][0])} characters")
            print(f"Time: {end_time - start_time:.2f} seconds")
            
        finally:
            if os.path.exists(test_file):
                os.unlink(test_file)

    def test_memory_usage_comparison_baseline(self, duckdb_conn):
        """Baseline test to compare memory usage patterns."""
        
        fd, test_file = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        
        try:
            file_size = self.create_large_json_with_unused_data(test_file, 100)
            
            print(f"\nBaseline comparison test:")
            print(f"File size: {file_size / 1024:.1f} KB")
            print(f"Records: 100")
            print(f"Unused data per record: ~8KB")
            
            # Test different query patterns
            scenarios = [
                ("SELECT COUNT(*)", "count aggregation"),
                ("SELECT SUM(id)", "sum aggregation"),
                ("SELECT id", "minimal projection"),
                ("SELECT id, small_field", "small multi-field projection"),
                ("SELECT large_unused_field_1", "large field projection"),
            ]
            
            for query, description in scenarios:
                start_time = time.time()
                result = duckdb_conn.execute(f'{query} FROM streaming_json_reader("{test_file}")').fetchall()
                end_time = time.time()
                
                print(f"SUCCESS: {description}: {len(result)} results in {end_time - start_time:.3f}s")

            print(f"SUCCESS: All baseline scenarios completed successfully")
            
        finally:
            if os.path.exists(test_file):
                os.unlink(test_file)

    @pytest.mark.limit_memory("30 MB")
    def test_streaming_vs_batch_memory_efficiency(self, duckdb_conn):
        """Test that streaming approach uses less memory than batch loading."""
        
        fd, test_file = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        
        try:
            file_size = self.create_large_json_with_unused_data(test_file, 300)
            
            print(f"\nStreaming vs batch memory test:")
            print(f"File size: {file_size / 1024 / 1024:.2f} MB")
            print(f"Memory limit: 30MB (tight constraint)")
            
            # This should work with streaming approach
            result = duckdb_conn.execute(f'SELECT COUNT(*) FROM streaming_json_reader("{test_file}")').fetchall()
            
            assert result[0][0] == 300
            
            print(f"SUCCESS: Streaming approach processed {result[0][0]} records within tight memory limit")
            
        finally:
            if os.path.exists(test_file):
                os.unlink(test_file)
